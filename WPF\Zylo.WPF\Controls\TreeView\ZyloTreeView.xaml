<UserControl
    d:DesignHeight="600"
    d:DesignWidth="400"
    mc:Ignorable="d"
    x:Class="Zylo.WPF.Controls.TreeView.ZyloTreeView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:behaviors="clr-namespace:Zylo.WPF.Behaviors"
    xmlns:converters="clr-namespace:Zylo.WPF.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dd="urn:gong-wpf-dragdrop"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:treeView="clr-namespace:Zylo.WPF.Controls.TreeView"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <UserControl.Resources>
        <!--  🔧 转换器资源  -->
        <converters:SimpleIconConverter x:Key="SimpleIconConverter" />
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

        <!--  🎨 TreeView节点模板 - 标准字体  -->
        <HierarchicalDataTemplate
            DataType="{x:Type treeView:TreeNodeData}"
            ItemsSource="{Binding Children}"
            x:Key="StandardTreeNodeTemplate">
            <Border
                Background="Transparent"
                CornerRadius="4"
                Padding="4,2">
                <StackPanel Orientation="Horizontal">
                    <!--  🎯 三图标支持 - 学习NavigationControl  -->
                    <ContentPresenter
                        Height="20"
                        Margin="0,0,6,0"
                        VerticalAlignment="Center"
                        Width="20">
                        <ContentPresenter.Content>
                            <MultiBinding>
                                <MultiBinding.Converter>
                                    <StaticResource ResourceKey="SimpleIconConverter" />
                                </MultiBinding.Converter>
                                <Binding Path="WpfUiSymbol" />
                                <Binding Path="ZyloSymbol" />
                                <Binding Path="Emoji" />
                                <Binding Path="DefaultIcon" />
                            </MultiBinding>
                        </ContentPresenter.Content>
                    </ContentPresenter>

                    <!--  节点文本信息  -->
                    <StackPanel Orientation="Vertical" VerticalAlignment="Center">
                        <!--  节点名称  -->
                        <TextBlock
                            FontSize="14"
                            FontWeight="Medium"
                            Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                            Text="{Binding Name}" />
                    </StackPanel>
                </StackPanel>
            </Border>
        </HierarchicalDataTemplate>

        <!--  🎨 TreeView节点模板 - 大字体版本  -->
        <HierarchicalDataTemplate
            DataType="{x:Type treeView:TreeNodeData}"
            ItemsSource="{Binding Children}"
            x:Key="LargeTreeNodeTemplate">
            <Border
                Background="Transparent"
                CornerRadius="6"
                Padding="8,4">
                <StackPanel Orientation="Horizontal">
                    <!--  🎯 三图标支持 - 大字体版本  -->
                    <ContentPresenter
                        Height="28"
                        Margin="0,0,10,0"
                        VerticalAlignment="Center"
                        Width="28">
                        <ContentPresenter.Content>
                            <MultiBinding>
                                <MultiBinding.Converter>
                                    <StaticResource ResourceKey="SimpleIconConverter" />
                                </MultiBinding.Converter>
                                <Binding Path="WpfUiSymbol" />
                                <Binding Path="ZyloSymbol" />
                                <Binding Path="Emoji" />
                                <Binding Path="DefaultIcon" />
                            </MultiBinding>
                        </ContentPresenter.Content>
                    </ContentPresenter>

                    <!--  节点文本信息  -->
                    <StackPanel Orientation="Vertical" VerticalAlignment="Center">
                        <!--  节点名称  -->
                        <TextBlock
                            FontSize="18"
                            FontWeight="Medium"
                            Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                            Text="{Binding Name}" />

                        <!--  节点类型标签 - 大字体版本独有  -->
                        <Border
                            Background="{DynamicResource AccentFillColorDefaultBrush}"
                            CornerRadius="8"
                            Margin="0,4,0,0"
                            Opacity="0.8"
                            Padding="6,2">
                            <TextBlock
                                FontSize="11"
                                FontWeight="Medium"
                                Foreground="White"
                                Text="{Binding NodeType}" />
                        </Border>
                    </StackPanel>
                </StackPanel>
            </Border>
        </HierarchicalDataTemplate>

        <!--  🎨 动态模板选择器 - 标准字体 vs 大字体  -->
        <Style TargetType="TreeView" x:Key="DynamicTreeViewStyle">
            <Setter Property="ItemTemplate" Value="{StaticResource StandardTreeNodeTemplate}" />
            <Style.Triggers>
                <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=IsLargeFont}" Value="True">
                    <Setter Property="ItemTemplate" Value="{StaticResource LargeTreeNodeTemplate}" />
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!--  搜索框区域  -->
        <ui:TextBox
            ClearButtonEnabled="True"
            Grid.Row="0"
            Icon="{ui:SymbolIcon Search20}"
            Margin="4"
            PlaceholderText="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=SearchPlaceholderText}"
            Text="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=FilterText, UpdateSourceTrigger=PropertyChanged}"
            Visibility="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=ShowSearchBox, Converter={StaticResource BooleanToVisibilityConverter}}" />

        <!--  TreeView 主体区域  -->
        <Border
            Background="Transparent"
            CornerRadius="8"
            Grid.Row="1"
            Margin="0">
            <DockPanel>
                <!--  🎨 使用动态TreeView样式 - 支持字体大小切换  -->
                <TreeView
                    Background="Transparent"
                    BorderThickness="0"
                    ItemsSource="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=FilteredTreeData}"
                    Margin="4,4,4,4"
                    Style="{StaticResource DynamicTreeViewStyle}"
                    VirtualizingPanel.IsVirtualizing="True"
                    VirtualizingPanel.VirtualizationMode="Recycling"
                    dd:DragDrop.DefaultDragAdornerOpacity="0.8"
                    dd:DragDrop.DragHandler="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DragHandler}"
                    dd:DragDrop.DropHandler="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=DropHandler}"
                    dd:DragDrop.IsDragSource="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=EnableDragDrop}"
                    dd:DragDrop.IsDropTarget="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=EnableDragDrop}"
                    dd:DragDrop.UseDefaultDragAdorner="True"
                    x:Name="MainTreeView">

                    <i:Interaction.Behaviors>
                        <!--  🎯 通用TreeView行为 - 支持多种绑定和命令  -->
                        <behaviors:TreeViewBehavior
                            ItemCollapsedCommand="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=NodeCollapsedCommand}"
                            ItemExpandedCommand="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=NodeExpandedCommand}"
                            SelectedItem="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=SelectedNode, Mode=TwoWay}"
                            SelectionChangedCommand="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=NodeSelectionChangedCommand}" />
                    </i:Interaction.Behaviors>

                    <!--  🎯 继承默认样式，只添加IsExpanded绑定  -->
                    <TreeView.ItemContainerStyle>
                        <Style BasedOn="{StaticResource {x:Type TreeViewItem}}" TargetType="TreeViewItem">
                            <Setter Property="IsExpanded" Value="{Binding IsExpanded, Mode=TwoWay}" />
                        </Style>
                    </TreeView.ItemContainerStyle>

                    <!--  右键菜单将通过代码动态设置  -->
                </TreeView>
            </DockPanel>
        </Border>
    </Grid>
</UserControl>
