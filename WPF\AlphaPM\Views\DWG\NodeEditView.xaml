<UserControl
    d:DataContext="{d:DesignInstance dwg:NodeEditViewModel}"
    d:DesignHeight="500"
    d:DesignWidth="600"
    mc:Ignorable="d"
    mvvm:ViewModelLocator.AutoWireViewModel="True"
    x:Class="AlphaPM.Views.DWG.NodeEditView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dwg="clr-namespace:AlphaPM.ViewModels.DWG"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:mvvm="http://prismlibrary.com/"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <UserControl.Resources>
        <!--  标签样式  -->
        <Style TargetType="TextBlock" x:Key="LabelStyle">
            <Setter Property="FontWeight" Value="Medium" />
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
            <Setter Property="Margin" Value="0,0,0,6" />
            <Setter Property="VerticalAlignment" Value="Center" />
        </Style>


    </UserControl.Resources>

    <!--  使用 Card 容器提供现代化外观  -->
    <ui:Card Margin="16" Padding="24">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!--  标题区域  -->
            <StackPanel Grid.Row="0" Margin="0,0,0,24">
                <TextBlock
                    FontSize="20"
                    FontWeight="SemiBold"
                    Text="{Binding WindowTitle}" />
                <!--  Foreground="{DynamicResource TextFillColorPrimaryBrush}"  -->
                <TextBlock
                    FontSize="14"
                    Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                    Margin="0,4,0,0"
                    Text="{Binding NodeTypeDisplayName, StringFormat='类型: {0}'}" />
            </StackPanel>

            <!--  表单区域  -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!--  节点名称  -->
                    <TextBlock Style="{StaticResource LabelStyle}" Text="节点名称 *" />
                    <ui:TextBox
                        PlaceholderText="请输入节点名称"
                        Margin="0,0,0,16"
                        MinHeight="36"
                        Text="{Binding NodeName, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}">
                        <ui:TextBox.Icon>
                            <ui:SymbolIcon Symbol="Tag24" />
                        </ui:TextBox.Icon>
                    </ui:TextBox>

                    <!--  节点描述  -->
                    <TextBlock Style="{StaticResource LabelStyle}" Text="节点描述" />
                    <ui:TextBox
                        AcceptsReturn="True"
                        Height="80"
                        PlaceholderText="请输入节点描述（可选）"
                        Margin="0,0,0,16"
                        Text="{Binding NodeDescription, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"
                        TextWrapping="Wrap"
                        VerticalScrollBarVisibility="Auto">
                        <ui:TextBox.Icon>
                            <ui:SymbolIcon Symbol="TextDescription24" />
                        </ui:TextBox.Icon>
                    </ui:TextBox>

                    <!--  提示信息  -->
                    <Border
                        Background="{DynamicResource InfoBarInformationalSeverityBackgroundBrush}"
                        BorderBrush="{DynamicResource InfoBarInformationalSeverityBorderBrush}"
                        BorderThickness="1"
                        CornerRadius="6"
                        Margin="0,8,0,0"
                        Padding="12,8">
                        <StackPanel Orientation="Horizontal">
                            <ui:SymbolIcon
                                FontSize="16"
                                Foreground="{DynamicResource InfoBarInformationalSeverityIconForegroundBrush}"
                                Margin="0,0,8,0"
                                Symbol="Info24" />
                            <TextBlock
                                Foreground="{DynamicResource InfoBarInformationalSeverityTitleForegroundBrush}"
                                Text="* 标记为必填项"
                                VerticalAlignment="Center" />
                        </StackPanel>
                    </Border>
                </StackPanel>
            </ScrollViewer>

            <!--  按钮区域  -->
            <Grid Grid.Row="2" Margin="0,24,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!--  取消按钮  -->
                <ui:Button
                    Appearance="Secondary"
                    Command="{Binding CancelCommand}"
                    Content="取消"
                    Grid.Column="1"
                    Icon="{ui:SymbolIcon Dismiss24}"
                    Margin="0,0,8,0"
                    MinWidth="100" />

                <!--  确认按钮  -->
                <ui:Button
                    Appearance="Primary"
                    Command="{Binding ConfirmCommand}"
                    Content="{Binding ConfirmButtonText}"
                    Grid.Column="2"
                    Icon="{ui:SymbolIcon Checkmark24}"
                    MinWidth="100" />
            </Grid>
        </Grid>
    </ui:Card>
</UserControl>
