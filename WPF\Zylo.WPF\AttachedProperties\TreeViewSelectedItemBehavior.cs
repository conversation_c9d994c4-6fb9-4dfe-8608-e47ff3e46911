
using TreeNodeData = Zylo.WPF.Controls.TreeView.TreeNodeData;

namespace Zylo.WPF.AttachedProperties;

/// <summary>
/// TreeView选中项双向绑定的依赖属性
/// 解决TreeView.SelectedItem不支持双向绑定的问题
/// </summary>
public static class TreeViewSelectedItemBehavior
{
    #region SelectedItem 依赖属性

    /// <summary>
    /// SelectedItem 依赖属性
    /// </summary>
    public static readonly DependencyProperty SelectedItemProperty =
        DependencyProperty.RegisterAttached(
            "SelectedItem",
            typeof(TreeNodeData),
            typeof(TreeViewSelectedItemBehavior),
            new FrameworkPropertyMetadata(
                null,
                FrameworkPropertyMetadataOptions.BindsTwoWayByDefault,
                OnSelectedItemChanged));

    /// <summary>
    /// 获取选中项
    /// </summary>
    public static TreeNodeData? GetSelectedItem(DependencyObject obj)
    {
        return (TreeNodeData?)obj.GetValue(SelectedItemProperty);
    }

    /// <summary>
    /// 设置选中项
    /// </summary>
    public static void SetSelectedItem(DependencyObject obj, TreeNodeData? value)
    {
        obj.SetValue(SelectedItemProperty, value);
    }

    /// <summary>
    /// 选中项变化处理
    /// </summary>
    private static void OnSelectedItemChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is TreeView treeView)
        {
            // 移除旧的事件处理
            treeView.SelectedItemChanged -= OnTreeViewSelectedItemChanged;
            
            // 添加新的事件处理
            treeView.SelectedItemChanged += OnTreeViewSelectedItemChanged;
            
            // 设置TreeView的选中项
            if (e.NewValue is TreeNodeData newSelectedItem)
            {
                SelectTreeViewItem(treeView, newSelectedItem);
            }
        }
    }

    /// <summary>
    /// TreeView选中项变化事件处理
    /// </summary>
    private static void OnTreeViewSelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
    {
        if (sender is TreeView treeView && e.NewValue is TreeNodeData selectedNode)
        {
            // 更新依赖属性的值
            SetSelectedItem(treeView, selectedNode);
        }
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 在TreeView中选中指定的节点
    /// </summary>
    private static void SelectTreeViewItem(TreeView treeView, TreeNodeData targetNode)
    {
        if (targetNode == null) return;

        // 递归查找并选中节点
        SelectNodeRecursive(treeView, targetNode);
    }

    /// <summary>
    /// 递归查找并选中节点
    /// </summary>
    private static bool SelectNodeRecursive(ItemsControl parent, TreeNodeData targetNode)
    {
        if (parent == null || targetNode == null) return false;

        foreach (var item in parent.Items)
        {
            if (item is TreeNodeData node && node.Id == targetNode.Id)
            {
                // 找到目标节点
                var container = parent.ItemContainerGenerator.ContainerFromItem(item) as TreeViewItem;
                if (container != null)
                {
                    container.IsSelected = true;
                    container.BringIntoView();
                    return true;
                }
            }

            // 递归查找子节点
            var childContainer = parent.ItemContainerGenerator.ContainerFromItem(item) as TreeViewItem;
            if (childContainer != null && SelectNodeRecursive(childContainer, targetNode))
            {
                childContainer.IsExpanded = true; // 展开父节点
                return true;
            }
        }

        return false;
    }

    #endregion
}
