using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Prism.Services.Dialogs;

using AlphaPM.Models;
using AlphaPM.Models.DWG;
using Zylo.WPF.Controls.TreeView;

namespace AlphaPM.ViewModels.DWG;

/// <summary>
/// 节点编辑视图模型 - 用于添加和修改树节点
/// </summary>
public partial class NodeEditViewModel : ObservableValidator, IDialogAware
{
    private readonly YLoggerInstance _logger = YLogger.ForDebug<NodeEditViewModel>();

    #region 属性


    [ObservableProperty] public partial TreeNodeData? SelectedNode { get; set; } = new();
    
    /// <summary>
    /// 节点名称
    /// </summary>
    [ObservableProperty]
    [Required(ErrorMessage = "节点名称不能为空")]
    [StringLength(50, ErrorMessage = "节点名称长度不能超过50个字符")]
    [Description("节点名称")]
    [NotifyCanExecuteChangedFor(nameof(ConfirmCommand))]  // 自动通知命令状态变化

    [NotifyPropertyChangedFor(nameof(ConfirmButtonText))] // 自动通知相关属性变化
    public partial string NodeName { get; set; } = string.Empty;

    /// <summary>
    /// 节点描述
    /// </summary>
    [ObservableProperty]
    [StringLength(200, ErrorMessage = "节点描述长度不能超过200个字符")]
    [Description("节点描述")]
    public partial string NodeDescription { get; set; } = string.Empty;

    /// <summary>
    /// 节点类型
    /// </summary>
    [ObservableProperty]
    [Description("节点类型")]
    [NotifyPropertyChangedFor(nameof(NodeTypeDisplayName))] // 自动通知显示名称变化
    public partial string NodeType { get; set; } = string.Empty;

    /// <summary>
    /// 节点图标 - 内部使用，不在界面显示
    /// </summary>
    private string _nodeIcon = string.Empty;
    public string NodeIcon
    {
        get => _nodeIcon;
        private set => _nodeIcon = value;
    }

    /// <summary>
    /// 是否为编辑模式
    /// </summary>
    [ObservableProperty]
    [Description("是否为编辑模式")]

    [NotifyPropertyChangedFor(nameof(ConfirmButtonText))] // 自动通知按钮文本变化
    public partial bool IsEditMode { get; set; }

    /// <summary>
    /// 窗口标题
    /// </summary>
    [ObservableProperty]
    [Description("窗口标题")]
    public partial string WindowTitle { get; set; } = "添加节点";

    /// <summary>
    /// 确认按钮文本
    /// </summary>
    public string ConfirmButtonText => IsEditMode ? "保存" : "添加";

    /// <summary>
    /// 节点类型显示名称
    /// </summary>
    public string NodeTypeDisplayName => DWGNodeTypes.GetDisplayName(NodeType);

    #endregion

    #region 命令

    /// <summary>
    /// 确认命令
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanConfirm))]
    private void Confirm()
    {
        ValidateAllProperties();
        if (HasErrors)
        {
            _logger.Warning("表单验证失败，无法提交");
            return;
        }

        var parameters = new DialogParameters
        {
            { "NodeName", NodeName },
            { "NodeDescription", NodeDescription },
            { "NodeType", NodeType },
            { "NodeIcon", NodeIcon }
        };

        RequestClose?.Invoke(new DialogResult(ButtonResult.OK, parameters));
    }

    /// <summary>
    /// 取消命令
    /// </summary>
    [RelayCommand]
    private void Cancel()
    {
        RequestClose?.Invoke(new DialogResult(ButtonResult.Cancel));
    }

    /// <summary>
    /// 是否可以确认
    /// </summary>
    private bool CanConfirm()
    {
        return !string.IsNullOrWhiteSpace(NodeName) && !HasErrors;
    }

    #endregion

    #region IDialogAware 实现

    public string Title => WindowTitle;

    public event Action<IDialogResult>? RequestClose;

    public bool CanCloseDialog() => true;

    public void OnDialogClosed()
    {
        _logger.Debug("节点编辑对话框已关闭");
    }

    public void OnDialogOpened(IDialogParameters parameters)
    {
        _logger.Debug("节点编辑对话框已打开");

        // 获取标题参数
        if (parameters.ContainsKey("Title"))
        {
            WindowTitle = parameters.GetValue<string>("Title");
            _logger.Debug($"📝 接收到标题参数: {WindowTitle}");
        }

        // 获取编辑模式参数
        if (parameters.ContainsKey("IsEditMode"))
        {
            IsEditMode = parameters.GetValue<bool>("IsEditMode");
            _logger.Debug($"📝 接收到编辑模式: {IsEditMode}");
        }
        
        if (parameters.ContainsKey("TreeNode"))
        {
            SelectedNode = parameters.GetValue<TreeNodeData>("TreeNode");
            _logger.Debug($"📝 接收到编辑模式: {SelectedNode}");
        }


        // 获取节点数据参数（编辑模式优先）
        if (parameters.ContainsKey("NodeData") && parameters.GetValue<TreeNodeData>("NodeData") is TreeNodeData nodeData)
        {
            // 编辑模式，填充现有数据
            NodeName = nodeData.Name;
            NodeDescription = nodeData.Description ?? string.Empty;
            NodeType = nodeData.NodeType;
            NodeIcon = nodeData.Emoji ?? string.Empty;
            _logger.Debug($"📝 编辑模式 - 加载节点数据: {nodeData.Name}, 描述: {NodeDescription}, 类型: {NodeType}");
        }
        else if (parameters.ContainsKey("NodeType"))
        {
            // 新建模式，只设置节点类型
            NodeType = parameters.GetValue<string>("NodeType");
            _logger.Debug($"📝 新建模式 - 节点类型: {NodeType}");

            // 根据节点类型自动设置图标
            if (NodeType == DWGNodeTypes.Folder)
            {
                NodeIcon = DWGIconManager.GetIconByLevel(1); // 年份或项目
            }
            else if (NodeType == DWGNodeTypes.Project)
            {
                NodeIcon = DWGIconManager.GetIconByLevel(3); // 栋号
            }
        }

        _logger.Debug($"📝 对话框初始化完成 - 标题: {WindowTitle}, 编辑模式: {IsEditMode}");
    }

    #endregion

    #region 属性变化处理

    /// <summary>
    /// 节点名称变化时的处理 - 只需要验证，通知已通过 [NotifyCanExecuteChangedFor] 自动处理
    /// </summary>
    partial void OnNodeNameChanged(string value)
    {
        ValidateProperty(value, nameof(NodeName));
        // 注意：ConfirmCommand.NotifyCanExecuteChanged() 已通过 [NotifyCanExecuteChangedFor] 自动处理
    }

    /// <summary>
    /// 节点描述变化时的处理 - 只需要验证
    /// </summary>
    partial void OnNodeDescriptionChanged(string value)
    {
        ValidateProperty(value, nameof(NodeDescription));
    }

    #endregion
}
