using Zylo.WPF.Controls.TreeView;

namespace AlphaPM.Models.DWG;

/// <summary>
/// DWG管理中的节点类型常量
/// </summary>
public static class DWGNodeTypes
{
    /// <summary>
    /// 文件夹类型 - 用于年份和项目名节点
    /// </summary>
    public const string Folder = "folder";
    
    /// <summary>
    /// 项目类型 - 用于具体栋号节点
    /// </summary>
    public const string Project = "project";
    
    /// <summary>
    /// 年份类型 - 顶层年份节点的特殊标识
    /// </summary>
    public const string Year = "year";
    
    /// <summary>
    /// 建筑类型 - 具体建筑栋号
    /// </summary>
    public const string Building = "building";
    
    /// <summary>
    /// 判断是否为文件夹类型节点（年份或项目）
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <returns>是否为文件夹类型</returns>
    public static bool IsFolder(string nodeType)
    {
        return nodeType?.ToLower() == Folder;
    }
    
    /// <summary>
    /// 判断是否为项目类型节点（栋号）
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <returns>是否为项目类型</returns>
    public static bool IsProject(string nodeType)
    {
        return nodeType?.ToLower() == Project;
    }
    
    /// <summary>
    /// 判断节点层级
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>节点层级（1=年份，2=项目，3=栋号）</returns>
    public static int GetNodeLevel(TreeNodeData node)
    {
        if (node.Parent == null) return 1; // 顶层年份
        if (node.Parent.Parent == null) return 2; // 二级项目
        return 3; // 三级栋号
    }
    
    /// <summary>
    /// 获取节点类型的显示名称
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <returns>显示名称</returns>
    public static string GetDisplayName(string nodeType)
    {
        return nodeType?.ToLower() switch
        {
            Folder => "文件夹",
            Project => "项目",
            Year => "年份",
            Building => "建筑",
            _ => "未知"
        };
    }



    /// <summary>
    /// 构建单个节点的完整路径
    /// </summary>
    /// <param name="node">目标节点</param>
    /// <returns>节点的完整路径</returns>
    /// <remarks>
    /// 🔍 路径构建逻辑：
    /// - 从当前节点开始，向上遍历到根节点
    /// - 收集所有父节点的名称
    /// - 按层级顺序组合成完整路径
    ///
    /// 📋 路径示例：
    /// - 根节点(年份)："/2024"
    /// - 二级节点(项目)："/2024/示例项目"
    /// - 三级节点(栋号)："/2024/示例项目/1#楼"
    /// </remarks>
    public static string BuildNodePath(TreeNodeData node)
    {
        if (node == null)
            return string.Empty;

        var pathParts = new List<string>();
        var currentNode = node;

        // 从当前节点向上遍历到根节点
        while (currentNode != null)
        {
            pathParts.Insert(0, currentNode.Name); // 插入到开头，保持正确的层级顺序
            currentNode = currentNode.Parent;
        }

        // 组合成完整路径，以 "/" 开头
        return "/" + string.Join("/", pathParts);
    }



}
