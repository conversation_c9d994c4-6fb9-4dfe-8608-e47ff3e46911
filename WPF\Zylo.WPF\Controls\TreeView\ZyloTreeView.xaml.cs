using System.Collections.ObjectModel;
using System.Windows.Input;
using GongSolutions.Wpf.DragDrop;

namespace Zylo.WPF.Controls.TreeView;

/// <summary>
/// 可重用的 TreeView 控件 - 基于 WPFTest TreeViewPageView 的最佳实践
/// 
/// 🎯 设计目标：
/// - 提供完整的 TreeView 功能（搜索、过滤、编辑等）
/// - 支持依赖属性绑定，便于在不同场景中使用
/// - 保持与原有样式和行为的一致性
/// - 支持命令绑定和事件处理
/// 
/// 📋 主要功能：
/// - 数据源绑定 (TreeData, FilteredTreeData)
/// - 选中项双向绑定 (SelectedNode)
/// - 搜索过滤 (FilterText)
/// - 节点操作命令 (展开、折叠、选择变化等)
/// - 样式切换支持 (IsLargeFont)
/// - 右键菜单支持
/// </summary>
public partial class ZyloTreeView : UserControl
{
    public ZyloTreeView()
    {
        InitializeComponent();

        // 控件加载完成后更新右键菜单
        this.Loaded += (s, e) =>
        {
            UpdateContextMenu();
            UpdateContextMenuFromItems();
        };
    }

    #region 数据源相关依赖属性

    /// <summary>
    /// 树形数据源
    /// </summary>
    public static readonly DependencyProperty TreeDataProperty =
        DependencyProperty.Register(
            nameof(TreeData),
            typeof(ObservableCollection<TreeNodeData>),
            typeof(ZyloTreeView),
            new FrameworkPropertyMetadata(null, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnTreeDataChanged));

    /// <summary>
    /// 树形数据源
    /// </summary>
    public ObservableCollection<TreeNodeData> TreeData
    {
        get => (ObservableCollection<TreeNodeData>)GetValue(TreeDataProperty);
        set => SetValue(TreeDataProperty, value);
    }

    /// <summary>
    /// 过滤后的树形数据（用于搜索）
    /// </summary>
    public static readonly DependencyProperty FilteredTreeDataProperty =
        DependencyProperty.Register(
            nameof(FilteredTreeData),
            typeof(ObservableCollection<TreeNodeData>),
            typeof(ZyloTreeView),
            new FrameworkPropertyMetadata(null, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault));

    /// <summary>
    /// 过滤后的树形数据（用于搜索）
    /// </summary>
    public ObservableCollection<TreeNodeData> FilteredTreeData
    {
        get => (ObservableCollection<TreeNodeData>)GetValue(FilteredTreeDataProperty);
        set => SetValue(FilteredTreeDataProperty, value);
    }

    #endregion

    #region 选中项相关依赖属性

    /// <summary>
    /// 选中的节点
    /// </summary>
    public static readonly DependencyProperty SelectedNodeProperty =
        DependencyProperty.Register(
            nameof(SelectedNode),
            typeof(TreeNodeData),
            typeof(ZyloTreeView),
            new FrameworkPropertyMetadata(null, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnSelectedNodeChanged));

    /// <summary>
    /// 选中的节点
    /// </summary>
    public TreeNodeData SelectedNode
    {
        get => (TreeNodeData)GetValue(SelectedNodeProperty);
        set => SetValue(SelectedNodeProperty, value);
    }

    #endregion

    #region 搜索过滤相关依赖属性

    /// <summary>
    /// 搜索过滤文本
    /// </summary>
    public static readonly DependencyProperty FilterTextProperty =
        DependencyProperty.Register(
            nameof(FilterText),
            typeof(string),
            typeof(ZyloTreeView),
            new FrameworkPropertyMetadata(string.Empty, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnFilterTextChanged));

    /// <summary>
    /// 搜索过滤文本
    /// </summary>
    public string FilterText
    {
        get => (string)GetValue(FilterTextProperty);
        set => SetValue(FilterTextProperty, value);
    }

    #endregion

    #region 样式相关依赖属性

    /// <summary>
    /// 是否使用大字体
    /// </summary>
    public static readonly DependencyProperty IsLargeFontProperty =
        DependencyProperty.Register(
            nameof(IsLargeFont),
            typeof(bool),
            typeof(ZyloTreeView),
            new FrameworkPropertyMetadata(false, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault));

    /// <summary>
    /// 是否使用大字体
    /// </summary>
    public bool IsLargeFont
    {
        get => (bool)GetValue(IsLargeFontProperty);
        set => SetValue(IsLargeFontProperty, value);
    }

    #endregion

    #region 命令相关依赖属性

    /// <summary>
    /// 节点选择变化命令
    /// </summary>
    public static readonly DependencyProperty NodeSelectionChangedCommandProperty =
        DependencyProperty.Register(
            nameof(NodeSelectionChangedCommand),
            typeof(ICommand),
            typeof(ZyloTreeView),
            new FrameworkPropertyMetadata(null));

    /// <summary>
    /// 节点选择变化命令
    /// </summary>
    public ICommand NodeSelectionChangedCommand
    {
        get => (ICommand)GetValue(NodeSelectionChangedCommandProperty);
        set => SetValue(NodeSelectionChangedCommandProperty, value);
    }

    /// <summary>
    /// 节点展开命令
    /// </summary>
    public static readonly DependencyProperty NodeExpandedCommandProperty =
        DependencyProperty.Register(
            nameof(NodeExpandedCommand),
            typeof(ICommand),
            typeof(ZyloTreeView),
            new FrameworkPropertyMetadata(null));

    /// <summary>
    /// 节点展开命令
    /// </summary>
    public ICommand NodeExpandedCommand
    {
        get => (ICommand)GetValue(NodeExpandedCommandProperty);
        set => SetValue(NodeExpandedCommandProperty, value);
    }

    /// <summary>
    /// 节点折叠命令
    /// </summary>
    public static readonly DependencyProperty NodeCollapsedCommandProperty =
        DependencyProperty.Register(
            nameof(NodeCollapsedCommand),
            typeof(ICommand),
            typeof(ZyloTreeView),
            new FrameworkPropertyMetadata(null));

    /// <summary>
    /// 节点折叠命令
    /// </summary>
    public ICommand NodeCollapsedCommand
    {
        get => (ICommand)GetValue(NodeCollapsedCommandProperty);
        set => SetValue(NodeCollapsedCommandProperty, value);
    }

    #endregion

    #region 右键菜单相关依赖属性

    /// <summary>
    /// 添加子节点命令
    /// </summary>
    public static readonly DependencyProperty AddChildNodeCommandProperty =
        DependencyProperty.Register(
            nameof(AddChildNodeCommand),
            typeof(ICommand),
            typeof(ZyloTreeView),
            new FrameworkPropertyMetadata(null));

    /// <summary>
    /// 添加子节点命令
    /// </summary>
    public ICommand AddChildNodeCommand
    {
        get => (ICommand)GetValue(AddChildNodeCommandProperty);
        set => SetValue(AddChildNodeCommandProperty, value);
    }

    /// <summary>
    /// 开始编辑节点命令
    /// </summary>
    public static readonly DependencyProperty StartEditNodeCommandProperty =
        DependencyProperty.Register(
            nameof(StartEditNodeCommand),
            typeof(ICommand),
            typeof(ZyloTreeView),
            new FrameworkPropertyMetadata(null));

    /// <summary>
    /// 开始编辑节点命令
    /// </summary>
    public ICommand StartEditNodeCommand
    {
        get => (ICommand)GetValue(StartEditNodeCommandProperty);
        set => SetValue(StartEditNodeCommandProperty, value);
    }

    /// <summary>
    /// 删除节点命令
    /// </summary>
    public static readonly DependencyProperty DeleteNodeCommandProperty =
        DependencyProperty.Register(
            nameof(DeleteNodeCommand),
            typeof(ICommand),
            typeof(ZyloTreeView),
            new FrameworkPropertyMetadata(null));

    /// <summary>
    /// 删除节点命令
    /// </summary>
    public ICommand DeleteNodeCommand
    {
        get => (ICommand)GetValue(DeleteNodeCommandProperty);
        set => SetValue(DeleteNodeCommandProperty, value);
    }

    #endregion

    #region 状态相关依赖属性

    /// <summary>
    /// 是否有选中节点
    /// </summary>
    public static readonly DependencyProperty HasSelectedNodeProperty =
        DependencyProperty.Register(
            nameof(HasSelectedNode),
            typeof(bool),
            typeof(ZyloTreeView),
            new FrameworkPropertyMetadata(false));

    /// <summary>
    /// 是否有选中节点
    /// </summary>
    public bool HasSelectedNode
    {
        get => (bool)GetValue(HasSelectedNodeProperty);
        set => SetValue(HasSelectedNodeProperty, value);
    }

    /// <summary>
    /// 是否可以编辑选中节点
    /// </summary>
    public static readonly DependencyProperty CanEditSelectedNodeProperty =
        DependencyProperty.Register(
            nameof(CanEditSelectedNode),
            typeof(bool),
            typeof(ZyloTreeView),
            new FrameworkPropertyMetadata(false));

    /// <summary>
    /// 是否可以编辑选中节点
    /// </summary>
    public bool CanEditSelectedNode
    {
        get => (bool)GetValue(CanEditSelectedNodeProperty);
        set => SetValue(CanEditSelectedNodeProperty, value);
    }

    /// <summary>
    /// 是否可以删除选中节点
    /// </summary>
    public static readonly DependencyProperty CanDeleteSelectedNodeProperty =
        DependencyProperty.Register(
            nameof(CanDeleteSelectedNode),
            typeof(bool),
            typeof(ZyloTreeView),
            new FrameworkPropertyMetadata(false));

    /// <summary>
    /// 是否可以删除选中节点
    /// </summary>
    public bool CanDeleteSelectedNode
    {
        get => (bool)GetValue(CanDeleteSelectedNodeProperty);
        set => SetValue(CanDeleteSelectedNodeProperty, value);
    }

    #endregion

    #region 显示控制相关依赖属性

    /// <summary>
    /// 是否显示搜索框
    /// </summary>
    public static readonly DependencyProperty ShowSearchBoxProperty =
        DependencyProperty.Register(
            nameof(ShowSearchBox),
            typeof(bool),
            typeof(ZyloTreeView),
            new FrameworkPropertyMetadata(true));

    /// <summary>
    /// 是否显示搜索框
    /// </summary>
    public bool ShowSearchBox
    {
        get => (bool)GetValue(ShowSearchBoxProperty);
        set => SetValue(ShowSearchBoxProperty, value);
    }

    /// <summary>
    /// 是否显示右键菜单
    /// </summary>
    public static readonly DependencyProperty ShowContextMenuProperty =
        DependencyProperty.Register(
            nameof(ShowContextMenu),
            typeof(bool),
            typeof(ZyloTreeView),
            new FrameworkPropertyMetadata(true));

    /// <summary>
    /// 是否显示右键菜单
    /// </summary>
    public bool ShowContextMenu
    {
        get => (bool)GetValue(ShowContextMenuProperty);
        set => SetValue(ShowContextMenuProperty, value);
    }

    /// <summary>
    /// 自定义右键菜单模板
    /// </summary>
    public static readonly DependencyProperty ContextMenuTemplateProperty =
        DependencyProperty.Register(
            nameof(ContextMenuTemplate),
            typeof(DataTemplate),
            typeof(ZyloTreeView),
            new FrameworkPropertyMetadata(null, OnContextMenuTemplateChanged));

    /// <summary>
    /// 自定义右键菜单模板
    /// </summary>
    public DataTemplate ContextMenuTemplate
    {
        get => (DataTemplate)GetValue(ContextMenuTemplateProperty);
        set => SetValue(ContextMenuTemplateProperty, value);
    }

    /// <summary>
    /// 动态右键菜单构建事件
    /// </summary>
    public event EventHandler<ContextMenuBuildingEventArgs>? ContextMenuBuilding;

    /// <summary>
    /// 右键菜单项集合
    /// </summary>
    public static readonly DependencyProperty ContextMenuItemsProperty =
        DependencyProperty.Register(
            nameof(ContextMenuItems),
            typeof(System.Collections.IEnumerable),
            typeof(ZyloTreeView),
            new FrameworkPropertyMetadata(null, OnContextMenuItemsChanged));

    /// <summary>
    /// 右键菜单项集合
    /// </summary>
    public System.Collections.IEnumerable ContextMenuItems
    {
        get => (System.Collections.IEnumerable)GetValue(ContextMenuItemsProperty);
        set => SetValue(ContextMenuItemsProperty, value);
    }

    /// <summary>
    /// 右键菜单构建事件参数
    /// </summary>
    public class ContextMenuBuildingEventArgs : EventArgs
    {
        public TreeNodeData? SelectedNode { get; set; }
        public ContextMenu? ContextMenu { get; set; }
    }

    /// <summary>
    /// 右键菜单模板变化时的处理
    /// </summary>
    private static void OnContextMenuTemplateChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ZyloTreeView control)
        {
            control.UpdateContextMenu();
        }
    }

    /// <summary>
    /// 右键菜单项集合变化时的处理
    /// </summary>
    private static void OnContextMenuItemsChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ZyloTreeView control)
        {
            control.UpdateContextMenuFromItems();
        }
    }

    /// <summary>
    /// 更新右键菜单
    /// </summary>
    private void UpdateContextMenu()
    {
        var treeView = this.FindName("MainTreeView") as System.Windows.Controls.TreeView;
        if (treeView == null) return;

        if (ContextMenuBuilding != null)
        {
            // 使用动态菜单构建事件
            treeView.ContextMenuOpening += OnContextMenuOpening;
        }
        else if (ContextMenuTemplate != null)
        {
            // 使用自定义模板创建右键菜单
            var contextMenu = new ContextMenu();
            var contentPresenter = new ContentPresenter
            {
                Content = this,
                ContentTemplate = ContextMenuTemplate
            };

            // 将 ContentPresenter 的内容添加到 ContextMenu
            contextMenu.Items.Add(contentPresenter);
            treeView.ContextMenu = contextMenu;
        }
        else
        {
            // 使用默认的右键菜单（已在 XAML 中定义）
            // 不需要额外操作，XAML 中的默认菜单会生效
        }
    }

    /// <summary>
    /// 右键菜单打开时的处理
    /// </summary>
    private void OnContextMenuOpening(object sender, ContextMenuEventArgs e)
    {
        if (ContextMenuBuilding == null) return;

        var contextMenu = new ContextMenu();
        var args = new ContextMenuBuildingEventArgs
        {
            SelectedNode = SelectedNode,
            ContextMenu = contextMenu
        };

        // 触发事件让外部构建菜单
        ContextMenuBuilding.Invoke(this, args);

        // 设置构建好的菜单
        if (sender is System.Windows.Controls.TreeView treeView)
        {
            treeView.ContextMenu = args.ContextMenu;
        }
    }

    /// <summary>
    /// 搜索框占位符文本
    /// </summary>
    public static readonly DependencyProperty SearchPlaceholderTextProperty =
        DependencyProperty.Register(
            nameof(SearchPlaceholderText),
            typeof(string),
            typeof(ZyloTreeView),
            new FrameworkPropertyMetadata("🔍 搜索节点..."));

    /// <summary>
    /// 搜索框占位符文本
    /// </summary>
    public string SearchPlaceholderText
    {
        get => (string)GetValue(SearchPlaceholderTextProperty);
        set => SetValue(SearchPlaceholderTextProperty, value);
    }

    #endregion

    #region 属性变化处理

    /// <summary>
    /// 选中节点变化时的处理
    /// </summary>
    /// <param name="d">依赖对象</param>
    /// <param name="e">属性变化事件参数</param>
    private static void OnSelectedNodeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ZyloTreeView control)
        {
            control.UpdateSelectedNodeState(e.NewValue as TreeNodeData);
        }
    }

    /// <summary>
    /// 更新选中节点相关状态
    /// </summary>
    /// <param name="selectedNode">选中的节点</param>
    private void UpdateSelectedNodeState(TreeNodeData? selectedNode)
    {
        HasSelectedNode = selectedNode != null;
        CanEditSelectedNode = selectedNode != null && selectedNode.IsEnabled;
        CanDeleteSelectedNode = selectedNode != null && selectedNode.IsEnabled && !selectedNode.IsRoot;
    }

    /// <summary>
    /// 过滤文本变化时的处理
    /// </summary>
    /// <param name="d">依赖对象</param>
    /// <param name="e">属性变化事件参数</param>
    private static void OnFilterTextChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ZyloTreeView control)
        {
            control.ApplyFilter();
        }
    }

    /// <summary>
    /// 树形数据变化时的处理
    /// </summary>
    /// <param name="d">依赖对象</param>
    /// <param name="e">属性变化事件参数</param>
    private static void OnTreeDataChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ZyloTreeView control)
        {
            control.ApplyFilter();
        }
    }

    #endregion

    #region 拖拽相关依赖属性

    /// <summary>
    /// 是否启用拖拽功能
    /// </summary>
    public static readonly DependencyProperty EnableDragDropProperty =
        DependencyProperty.Register(
            nameof(EnableDragDrop),
            typeof(bool),
            typeof(ZyloTreeView),
            new FrameworkPropertyMetadata(false));

    /// <summary>
    /// 是否启用拖拽功能
    /// </summary>
    public bool EnableDragDrop
    {
        get => (bool)GetValue(EnableDragDropProperty);
        set => SetValue(EnableDragDropProperty, value);
    }

    /// <summary>
    /// 拖拽处理器
    /// </summary>
    public static readonly DependencyProperty DragHandlerProperty =
        DependencyProperty.Register(
            nameof(DragHandler),
            typeof(IDragSource),
            typeof(ZyloTreeView),
            new FrameworkPropertyMetadata(null));

    /// <summary>
    /// 拖拽处理器
    /// </summary>
    public IDragSource DragHandler
    {
        get => (IDragSource)GetValue(DragHandlerProperty);
        set => SetValue(DragHandlerProperty, value);
    }

    /// <summary>
    /// 放置处理器
    /// </summary>
    public static readonly DependencyProperty DropHandlerProperty =
        DependencyProperty.Register(
            nameof(DropHandler),
            typeof(IDropTarget),
            typeof(ZyloTreeView),
            new FrameworkPropertyMetadata(null));

    /// <summary>
    /// 放置处理器
    /// </summary>
    public IDropTarget DropHandler
    {
        get => (IDropTarget)GetValue(DropHandlerProperty);
        set => SetValue(DropHandlerProperty, value);
    }

    #endregion


    #region 内部逻辑方法

    /// <summary>
    /// 应用过滤器
    /// </summary>
    private void ApplyFilter()
    {
        if (TreeData == null)
        {
            FilteredTreeData = new ObservableCollection<TreeNodeData>();
            return;
        }

        if (string.IsNullOrWhiteSpace(FilterText))
        {
            // 没有过滤条件，显示所有数据
            FilteredTreeData = TreeData;
        }
        else
        {
            // 有过滤条件，进行过滤
            var filteredData = new ObservableCollection<TreeNodeData>();
            foreach (var rootNode in TreeData)
            {
                var filteredNode = FilterNode(rootNode, FilterText);
                if (filteredNode != null)
                {
                    filteredData.Add(filteredNode);
                }
            }
            FilteredTreeData = filteredData;
        }
    }

    /// <summary>
    /// 过滤单个节点
    /// </summary>
    /// <param name="node">要过滤的节点</param>
    /// <param name="filterText">过滤文本</param>
    /// <returns>过滤后的节点，如果不匹配则返回null</returns>
    private TreeNodeData? FilterNode(TreeNodeData node, string filterText)
    {
        if (node == null) return null;

        var isMatch = node.Name?.Contains(filterText, StringComparison.OrdinalIgnoreCase) == true ||
                      node.Description?.Contains(filterText, StringComparison.OrdinalIgnoreCase) == true;

        var filteredChildren = new ObservableCollection<TreeNodeData>();
        if (node.Children != null)
        {
            foreach (var child in node.Children)
            {
                var filteredChild = FilterNode(child, filterText);
                if (filteredChild != null)
                {
                    filteredChildren.Add(filteredChild);
                }
            }
        }

        // 如果当前节点匹配或有匹配的子节点，则包含此节点
        if (isMatch || filteredChildren.Count > 0)
        {
            var filteredNode = new TreeNodeData
            {
                Id = node.Id,
                Name = node.Name,
                Description = node.Description,
                NodeType = node.NodeType,
                Category = node.Category,
                Tags = new List<string>(node.Tags),
                IsExpanded = node.IsExpanded || filteredChildren.Count > 0, // 有匹配子节点时自动展开
                IsEnabled = node.IsEnabled,
                IsVisible = node.IsVisible,
                WpfUiSymbol = node.WpfUiSymbol,
                ZyloSymbol = node.ZyloSymbol,
                Emoji = node.Emoji,
                Children = filteredChildren,
                Parent = node.Parent,
                SortOrder = node.SortOrder,
                CustomData = new Dictionary<string, object>(node.CustomData)
            };

            return filteredNode;
        }

        return null;
    }

    /// <summary>
    /// 从菜单项集合更新右键菜单
    /// </summary>
    private void UpdateContextMenuFromItems()
    {
        var treeView = this.FindName("MainTreeView") as System.Windows.Controls.TreeView;
        if (treeView == null)
        {
            return;
        }

        // 如果没有 ContextMenuItems 或为空，使用默认菜单
        if (ContextMenuItems == null || !ContextMenuItems.Cast<object>().Any())
        {
            CreateDefaultContextMenu(treeView);
            return;
        }

        // 创建动态菜单
        var contextMenu = new ContextMenu();

        foreach (var item in ContextMenuItems)
        {
            // 使用反射获取菜单项属性，因为我们不能直接引用 AlphaPM 项目的类型
            var itemType = item.GetType();
            var headerProperty = itemType.GetProperty("Header");
            var commandProperty = itemType.GetProperty("Command");
            var isEnabledProperty = itemType.GetProperty("IsEnabled");
            var isSeparatorProperty = itemType.GetProperty("IsSeparator");

            if (isSeparatorProperty?.GetValue(item) is true)
            {
                // 添加分隔符
                contextMenu.Items.Add(new Separator());
            }
            else
            {
                // 添加菜单项
                var menuItem = new MenuItem();

                if (headerProperty?.GetValue(item) is string header)
                {
                    menuItem.Header = header;
                }

                if (commandProperty?.GetValue(item) is System.Windows.Input.ICommand command)
                    menuItem.Command = command;

                if (isEnabledProperty?.GetValue(item) is bool isEnabled)
                    menuItem.IsEnabled = isEnabled;

                contextMenu.Items.Add(menuItem);
            }
        }

        treeView.ContextMenu = contextMenu;
    }

    /// <summary>
    /// 创建默认右键菜单
    /// </summary>
    private void CreateDefaultContextMenu(System.Windows.Controls.TreeView treeView)
    {
        if (!ShowContextMenu) return;

        var contextMenu = new ContextMenu();

        // 默认菜单项
        contextMenu.Items.Add(new MenuItem
        {
            Header = "➕ 添加子节点",
            Command = AddChildNodeCommand,
            IsEnabled = HasSelectedNode
        });

        contextMenu.Items.Add(new MenuItem
        {
            Header = "✏️ 编辑节点",
            Command = StartEditNodeCommand,
            IsEnabled = CanEditSelectedNode
        });

        contextMenu.Items.Add(new Separator());

        contextMenu.Items.Add(new MenuItem
        {
            Header = "🗑️ 删除节点",
            Command = DeleteNodeCommand,
            IsEnabled = CanDeleteSelectedNode
        });

        treeView.ContextMenu = contextMenu;
    }

    #endregion


}
