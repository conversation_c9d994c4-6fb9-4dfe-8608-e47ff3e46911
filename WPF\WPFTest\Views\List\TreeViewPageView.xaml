﻿<UserControl
    d:DataContext="{d:DesignInstance list:TreeViewPageViewModel}"
    d:DesignHeight="2000"
    d:DesignWidth="1200"
    mc:Ignorable="d"
    mvvm:ViewModelLocator.AutoWireViewModel="True"
    x:Class="WPFTest.Views.List.TreeViewPageView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:behaviors="clr-namespace:Zylo.WPF.Behaviors;assembly=Zylo.WPF"
    xmlns:converters="clr-namespace:Zylo.WPF.Converters;assembly=Zylo.WPF"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:list="clr-namespace:WPFTest.ViewModels.List"
    xmlns:local="clr-namespace:WPFTest.Views.List"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:models="clr-namespace:WPFTest.Models"
    xmlns:mvvm="http://prismlibrary.com/"
    xmlns:navigation="clr-namespace:Zylo.WPF.Behaviors.Navigation;assembly=Zylo.WPF"
    xmlns:treeView="clr-namespace:Zylo.WPF.Controls.TreeView;assembly=Zylo.WPF"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <UserControl.Resources>
        <!--  🔧 转换器资源  -->
        <converters:SimpleIconConverter x:Key="SimpleIconConverter" />

        <!--  🎨 TreeView节点模板 - 标准字体  -->
        <HierarchicalDataTemplate
            DataType="{x:Type treeView:TreeNodeData}"
            ItemsSource="{Binding Children}"
            x:Key="StandardTreeNodeTemplate">
            <Border
                Background="Transparent"
                CornerRadius="4"
                Padding="4,2">
                <StackPanel Orientation="Horizontal">
                    <!--  🎯 三图标支持 - 学习NavigationControl  -->
                    <ContentPresenter
                        Height="20"
                        Margin="0,0,6,0"
                        VerticalAlignment="Center"
                        Width="20">
                        <ContentPresenter.Content>
                            <MultiBinding>
                                <MultiBinding.Converter>
                                    <StaticResource ResourceKey="SimpleIconConverter" />
                                </MultiBinding.Converter>
                                <Binding Path="WpfUiSymbol" />
                                <Binding Path="ZyloSymbol" />
                                <Binding Path="Emoji" />
                                <Binding Path="DefaultIcon" />
                            </MultiBinding>
                        </ContentPresenter.Content>
                    </ContentPresenter>

                    <!--  节点文本信息  -->
                    <StackPanel Orientation="Vertical" VerticalAlignment="Center">
                        <!--  节点名称  -->
                        <TextBlock
                            FontSize="14"
                            FontWeight="Medium"
                            Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                            Text="{Binding Name}" />

                        <!--  节点描述  -->
                        <!--  <TextBlock Text="{Binding Description}"  -->
                        <!--  FontSize="12"  -->
                        <!--  Opacity="0.7"  -->
                        <!--  Foreground="{DynamicResource TextFillColorSecondaryBrush}"  -->
                        <!--  Visibility="{Binding Description, Converter={StaticResource StringToVisibilityConverter}}" />  -->
                    </StackPanel>
                </StackPanel>
            </Border>
        </HierarchicalDataTemplate>



        <!--  🎨 TreeView节点模板 - 大字体版本  -->
        <HierarchicalDataTemplate
            DataType="{x:Type treeView:TreeNodeData}"
            ItemsSource="{Binding Children}"
            x:Key="LargeTreeNodeTemplate">
            <Border
                Background="Transparent"
                CornerRadius="6"
                Padding="8,4">
                <StackPanel Orientation="Horizontal">
                    <!--  🎯 三图标支持 - 大字体版本  -->
                    <ContentPresenter
                        Height="28"
                        Margin="0,0,10,0"
                        VerticalAlignment="Center"
                        Width="28">
                        <ContentPresenter.Content>
                            <MultiBinding>
                                <MultiBinding.Converter>
                                    <StaticResource ResourceKey="SimpleIconConverter" />
                                </MultiBinding.Converter>
                                <Binding Path="WpfUiSymbol" />
                                <Binding Path="ZyloSymbol" />
                                <Binding Path="Emoji" />
                                <Binding Path="DefaultIcon" />
                            </MultiBinding>
                        </ContentPresenter.Content>
                    </ContentPresenter>

                    <!--  节点文本信息 - 大字体版本  -->
                    <StackPanel Orientation="Vertical" VerticalAlignment="Center">
                        <!--  节点名称 - 大字体  -->
                        <TextBlock
                            FontSize="18"
                            FontWeight="SemiBold"
                            Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                            Text="{Binding Name}" />

                        <!--  节点描述 - 大字体  -->
                        <TextBlock
                            FontSize="15"
                            Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                            Margin="0,2,0,0"
                            Opacity="0.7"
                            Text="{Binding Description}"
                            Visibility="{Binding Description, Converter={StaticResource StringToVisibilityConverter}}" />

                        <!--  节点类型标签 - 大字体版本独有  -->
                        <Border
                            Background="{DynamicResource AccentFillColorDefaultBrush}"
                            CornerRadius="8"
                            Margin="0,4,0,0"
                            Opacity="0.8"
                            Padding="6,2">
                            <TextBlock
                                FontSize="11"
                                FontWeight="Medium"
                                Foreground="White"
                                Text="{Binding NodeType}" />
                        </Border>
                    </StackPanel>
                </StackPanel>
            </Border>
        </HierarchicalDataTemplate>

        <!--  🎨 动态模板选择器 - 标准字体 vs 大字体  -->
        <Style TargetType="TreeView" x:Key="DynamicTreeViewStyle">
            <Setter Property="ItemTemplate" Value="{StaticResource StandardTreeNodeTemplate}" />
            <Style.Triggers>
                <DataTrigger Binding="{Binding ElementName=LargeFontToggle, Path=IsChecked}" Value="True">
                    <Setter Property="ItemTemplate" Value="{StaticResource LargeTreeNodeTemplate}" />
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!--  加载指示器样式  -->
        <Style TargetType="ui:ProgressRing" x:Key="LoadingIndicatorStyle">
            <Setter Property="Width" Value="20" />
            <Setter Property="Height" Value="20" />
            <Setter Property="Margin" Value="8,0" />
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  标题和操作栏  -->
        <ui:Card
            Grid.Row="0"
            Margin="8"
            Padding="16">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!--  标题行  -->
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <!--  标题和状态  -->
                    <StackPanel Grid.Column="0">
                        <TextBlock
                            FontSize="20"
                            FontWeight="SemiBold"
                            Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                            Margin="0,0,0,4"
                            Text="🌳 TreeViewPageView 完整功能示例" />

                        <StackPanel Orientation="Horizontal">
                            <TextBlock
                                FontSize="12"
                                Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                Text="{Binding TreeDataInfo}" />

                            <!--  加载指示器  -->
                            <ui:ProgressRing
                                IsIndeterminate="True"
                                Style="{StaticResource LoadingIndicatorStyle}"
                                Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}" />
                        </StackPanel>
                    </StackPanel>

                    <!--  搜索框  -->
                    <ui:TextBox
                        ClearButtonEnabled="True"
                        Grid.Column="1"
                        Icon="{ui:SymbolIcon Search20}"
                        PlaceholderText="🔍 搜索节点..."
                        Text="{Binding FilterText, UpdateSourceTrigger=PropertyChanged}"
                        Width="280" />
                </Grid>

                <!--  操作按钮区域 - 分行显示  -->
                <StackPanel Grid.Row="1" Margin="0,12,0,0">

                    <!--  第一行：基础操作  -->
                    <StackPanel Margin="0,0,0,8" Orientation="Horizontal">
                        <TextBlock
                            FontWeight="SemiBold"
                            Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                            Margin="0,0,12,0"
                            Text="基础操作："
                            VerticalAlignment="Center" />

                        <ui:Button
                            Appearance="Primary"
                            Command="{Binding AddRootNodeCommand}"
                            Content="🌳 添加根节点"
                            Icon="{ui:SymbolIcon Add20}"
                            Margin="0,0,8,0" />

                        <ui:Button
                            Command="{Binding AddChildNodeCommand}"
                            Content="➕ 添加子节点"
                            Icon="{ui:SymbolIcon AddSquare20}"
                            IsEnabled="{Binding HasSelectedNode}"
                            Margin="0,0,8,0" />

                        <ui:Button
                            Command="{Binding StartEditNodeCommand}"
                            Content="✏️ 编辑节点"
                            Icon="{ui:SymbolIcon Edit20}"
                            IsEnabled="{Binding CanEditSelectedNode}"
                            Margin="0,0,8,0" />

                        <ui:Button
                            Command="{Binding DeleteNodeCommand}"
                            Content="🗑️ 删除节点"
                            Icon="{ui:SymbolIcon Delete20}"
                            IsEnabled="{Binding CanDeleteSelectedNode}"
                            Margin="0,0,8,0" />
                    </StackPanel>

                    <!--  第二行：展开/折叠和数据操作  -->
                    <StackPanel Margin="0,0,0,8" Orientation="Horizontal">
                        <TextBlock
                            FontWeight="SemiBold"
                            Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                            Margin="0,0,12,0"
                            Text="视图操作："
                            VerticalAlignment="Center" />

                        <ui:Button
                            Command="{Binding ExpandAllNodesCommand}"
                            Content="📂 展开全部"
                            Icon="{ui:SymbolIcon ChevronDown20}"
                            IsEnabled="{Binding HasTreeData}"
                            Margin="0,0,8,0" />

                        <ui:Button
                            Command="{Binding CollapseAllNodesCommand}"
                            Content="📁 折叠全部"
                            Icon="{ui:SymbolIcon ChevronUp20}"
                            IsEnabled="{Binding HasTreeData}"
                            Margin="0,0,8,0" />

                        <ui:Button
                            Command="{Binding ReloadTestDataCommand}"
                            Content="🔄 重新加载"
                            Icon="{ui:SymbolIcon ArrowClockwise20}"
                            Margin="0,0,8,0" />

                        <ui:Button
                            Command="{Binding ClearAllDataCommand}"
                            Content="🗑️ 清空数据"
                            Icon="{ui:SymbolIcon DeleteDismiss20}"
                            Margin="0,0,8,0" />

                        <!--  🔤 字体大小切换  -->
                        <ToggleButton
                            Content="🔤 大字体"
                            Margin="0,0,8,0"
                            ToolTip="切换大字体模式"
                            x:Name="LargeFontToggle" />
                    </StackPanel>

                    <!--  第三行：演示功能  -->
                    <StackPanel Margin="0,0,0,8" Orientation="Horizontal">
                        <TextBlock
                            FontWeight="SemiBold"
                            Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                            Margin="0,0,12,0"
                            Text="演示功能："
                            VerticalAlignment="Center" />

                        <ui:Button
                            Command="{Binding ReloadWithConstructorsCommand}"
                            Content="🏗️ 便利构造"
                            Icon="{ui:SymbolIcon Wrench20}"
                            IsEnabled="{Binding CanReloadData}"
                            Margin="0,0,8,0"
                            ToolTip="使用便利构造函数创建数据" />

                        <ui:Button
                            Command="{Binding ShowExtendedIconDemoCommand}"
                            Content="🎨 扩展图标"
                            Icon="{ui:SymbolIcon Emoji20}"
                            IsEnabled="{Binding CanReloadData}"
                            Margin="0,0,8,0"
                            ToolTip="展示扩展的图标类型支持" />

                        <ui:Button
                            Command="{Binding TestSelectedNodeCommand}"
                            Content="🔍 测试选中"
                            Icon="{ui:SymbolIcon Bug20}"
                            Margin="0,0,8,0"
                            ToolTip="调试功能：测试选中节点" />
                    </StackPanel>

                    <!--  第四行：状态保存功能  -->
                    <StackPanel Orientation="Horizontal">
                        <TextBlock
                            FontWeight="SemiBold"
                            Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                            Margin="0,0,12,0"
                            Text="状态管理："
                            VerticalAlignment="Center" />

                        <ui:Button
                            Command="{Binding SaveExpandedStateCommand}"
                            Content="💾 保存状态"
                            Icon="{ui:SymbolIcon Save20}"
                            IsEnabled="{Binding CanPerformActions}"
                            Margin="0,0,8,0"
                            ToolTip="保存当前展开状态到JSON文件" />

                        <ui:Button
                            Command="{Binding LoadExpandedStateCommand}"
                            Content="📂 读取状态"
                            Icon="{ui:SymbolIcon FolderOpen20}"
                            IsEnabled="{Binding CanPerformActions}"
                            Margin="0,0,8,0"
                            ToolTip="从JSON文件读取展开状态" />

                        <ui:Button
                            Command="{Binding ClearSavedStateCommand}"
                            Content="🗑️ 清除状态"
                            Icon="{ui:SymbolIcon Delete20}"
                            IsEnabled="{Binding CanPerformActions}"
                            Margin="0,0,8,0"
                            ToolTip="清除保存的状态文件" />
                    </StackPanel>
                </StackPanel>
            </Grid>
        </ui:Card>

        <!--  🎨 主内容区域 - 现代化设计  -->
        <Grid Grid.Row="1" Margin="16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition MinWidth="400" Width="3*" />
                <ColumnDefinition Width="16" />
                <ColumnDefinition MinWidth="300" Width="2*" />
            </Grid.ColumnDefinitions>

            <!--  🔥 TreeView区域 - 强制占满，不用Card  -->
            <Border
                Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                BorderThickness="1"
                CornerRadius="8"
                Grid.Column="0">
                <DockPanel>
                    <!--  标题栏  -->
                    <Border
                        Background="{DynamicResource AccentFillColorDefaultBrush}"
                        CornerRadius="8,8,0,0"
                        DockPanel.Dock="Top"
                        Padding="16,12">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                <TextBlock
                                    FontSize="18"
                                    Margin="0,0,8,0"
                                    Text="🌳" />
                                <TextBlock
                                    FontSize="14"
                                    FontWeight="SemiBold"
                                    Foreground="White"
                                    Text="组织架构树" />
                            </StackPanel>

                            <TextBlock
                                FontSize="11"
                                Foreground="White"
                                Grid.Column="1"
                                Opacity="0.8"
                                Text="{Binding TreeDataInfo}"
                                VerticalAlignment="Center" />
                        </Grid>
                    </Border>

                    <!--  🎨 使用动态TreeView样式 - 支持字体大小切换  -->
                    <TreeView
                        Background="Transparent"
                        BorderThickness="0"
                        ItemsSource="{Binding FilteredTreeData}"
                        Margin="12"
                        Style="{StaticResource DynamicTreeViewStyle}"
                        VirtualizingPanel.IsVirtualizing="True"
                        VirtualizingPanel.VirtualizationMode="Recycling"
                        x:Name="MainTreeView">

                        <i:Interaction.Behaviors>
                            <!--  🎯 通用TreeView行为 - 支持多种绑定和命令  -->
                            <behaviors:TreeViewBehavior
                                ItemCollapsedCommand="{Binding NodeCollapsedCommand}"
                                ItemExpandedCommand="{Binding NodeExpandedCommand}"
                                SelectedItem="{Binding SelectedNode, Mode=TwoWay}"
                                SelectionChangedCommand="{Binding NodeSelectionChangedCommand}" />
                        </i:Interaction.Behaviors>

                        <!--  🎯 继承默认样式，只添加IsExpanded绑定  -->
                        <TreeView.ItemContainerStyle>
                            <Style BasedOn="{StaticResource {x:Type TreeViewItem}}" TargetType="TreeViewItem">
                                <Setter Property="IsExpanded" Value="{Binding IsExpanded, Mode=TwoWay}" />
                            </Style>
                        </TreeView.ItemContainerStyle>

                        <!--  精美右键菜单  -->
                        <TreeView.ContextMenu>
                            <ContextMenu>
                                <MenuItem
                                    Command="{Binding AddChildNodeCommand}"
                                    Header="➕ 添加子节点"
                                    IsEnabled="{Binding HasSelectedNode}" />
                                <MenuItem
                                    Command="{Binding StartEditNodeCommand}"
                                    Header="✏️ 编辑节点"
                                    IsEnabled="{Binding CanEditSelectedNode}" />
                                <Separator />
                                <MenuItem
                                    Command="{Binding DeleteNodeCommand}"
                                    Header="🗑️ 删除节点"
                                    IsEnabled="{Binding CanDeleteSelectedNode}" />
                            </ContextMenu>
                        </TreeView.ContextMenu>
                    </TreeView>
                </DockPanel>
            </Border>

            <!--  优雅分隔线  -->
            <Rectangle
                Fill="{DynamicResource ControlStrokeColorDefaultBrush}"
                Grid.Column="1"
                HorizontalAlignment="Center"
                Opacity="0.3"
                Width="1" />

            <!--  📝 右侧详情面板 - 现代设计  -->
            <ui:Card Grid.Column="2" Padding="0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <!--  详情标题栏  -->
                    <Border
                        Background="{DynamicResource AccentFillColorSecondaryBrush}"
                        CornerRadius="8,8,0,0"
                        Grid.Row="0"
                        Padding="16,12">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock
                                FontSize="16"
                                Margin="0,0,8,0"
                                Text="📝" />
                            <TextBlock
                                FontSize="14"
                                FontWeight="SemiBold"
                                Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                Text="节点详情" />
                        </StackPanel>
                    </Border>

                    <!--  详情内容区域  -->
                    <ScrollViewer
                        Grid.Row="1"
                        Padding="16"
                        VerticalAlignment="Top"
                        VerticalScrollBarVisibility="Auto">
                        <Border
                            Background="{DynamicResource SubtleFillColorSecondaryBrush}"
                            CornerRadius="6"
                            MinHeight="100"
                            Padding="16">
                            <TextBlock
                                FontSize="13"
                                Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                LineHeight="22"
                                Text="{Binding SelectedNodeDetails}"
                                TextWrapping="Wrap" />
                        </Border>
                    </ScrollViewer>
                </Grid>
            </ui:Card>
        </Grid>

        <!--  🎯 状态栏 - 只显示操作状态  -->
        <Border
            Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
            CornerRadius="6"
            Grid.Row="2"
            Margin="16,0,16,16"
            Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <TextBlock
                    FontSize="12"
                    Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                    Grid.Column="0"
                    Text="{Binding StatusMessage}"
                    VerticalAlignment="Center" />

                <TextBlock
                    FontSize="12"
                    FontWeight="Medium"
                    Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                    Grid.Column="1"
                    Text="{Binding TreeDataInfo}"
                    VerticalAlignment="Center" />
            </Grid>
        </Border>
    </Grid>
</UserControl>